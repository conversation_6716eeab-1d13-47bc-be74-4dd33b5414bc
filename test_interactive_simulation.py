#!/usr/bin/env python3
"""
Simulate interactive mode testing for the refactored agent.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from idea_research_agent import IdeaResearchAgent
from idea_research_agent.utils.config import load_config
from idea_research_agent.utils.logger import setup_logger

async def simulate_interactive_session():
    """Simulate an interactive session with multiple topics."""
    
    setup_logger(level="INFO")
    
    print("🎯 Simulating Interactive Session")
    print("=" * 50)
    
    # Test topics
    test_topics = [
        "healthcare AI",
        "sustainable energy",
        "remote work tools"
    ]
    
    try:
        config = load_config()
        agent = IdeaResearchAgent(config)
        
        print(f"✅ Agent initialized with model: {config.default_model}")
        print(f"📊 Quality threshold: {config.quality_threshold}")
        print(f"🔄 Max iterations: {config.max_iterations}")
        
        for i, topic in enumerate(test_topics, 1):
            print(f"\n🚀 Test {i}/3: Generating idea for '{topic}'")
            print("-" * 40)
            
            # Test the workflow
            result = await agent.run_idea_generation(topic)
            
            if result and not result.startswith("Error:"):
                print(f"✅ Success! Generated {len(result)} characters")
                print(f"📝 Preview: {result[:150]}...")
                
                # Test structured output
                structured = await agent.generate_business_idea(topic)
                if structured.get("success"):
                    score = structured.get("final_evaluation", {}).get("overall_score", "N/A")
                    print(f"📊 Structured result score: {score}")
                else:
                    print(f"❌ Structured result failed: {structured.get('error')}")
            else:
                print(f"❌ Failed: {result}")
            
            print()
        
        print("🎉 Interactive simulation completed successfully!")
        
    except Exception as e:
        print(f"❌ Simulation failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simulate_interactive_session())
